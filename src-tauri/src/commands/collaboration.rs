use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;
use tauri::State;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollaborativeSession {
    pub id: String,
    pub session_id: String,
    pub project_path: String,
    pub session_code: String,
    pub share_url: String,
    pub ws_url: String,
    pub owner_id: String,
    pub created_at: i64,
    pub collaborators: Vec<Collaborator>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Collaborator {
    pub id: String,
    pub name: String,
    pub email: String,
    pub avatar: Option<String>,
    pub role: CollaboratorRole,
    pub status: CollaboratorStatus,
    pub cursor: Option<CursorPosition>,
    pub color: String,
    pub joined_at: i64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum CollaboratorRole {
    Owner,
    Editor,
    Viewer,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum CollaboratorStatus {
    Online,
    Offline,
    Away,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPosition {
    pub line: u32,
    pub column: u32,
    pub file: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionPermissions {
    pub can_edit: bool,
    pub can_invite: bool,
    pub can_remove: bool,
    pub can_chat: bool,
    pub can_voice: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InviteRequest {
    pub email: String,
    pub role: CollaboratorRole,
}

#[derive(Debug, Clone, Default)]
pub struct CollaborationState {
    pub sessions: Arc<RwLock<HashMap<String, CollaborativeSession>>>,
    pub connections: Arc<RwLock<HashMap<String, Vec<String>>>>, // session_id -> connection_ids
}

impl CollaborationState {
    pub fn new() -> Self {
        Self::default()
    }
}

#[tauri::command]
pub async fn start_collaborative_session(
    session_id: String,
    project_path: String,
    use_local_backend: Option<bool>,
    local_ws_url: Option<String>,
    local_share_url: Option<String>,
    state: State<'_, CollaborationState>,
) -> Result<CollaborativeSession, String> {
    let session_code = generate_session_code();
    
    // Use local URLs if specified, otherwise fall back to production
    let (share_url, ws_url) = if use_local_backend.unwrap_or(true) {
        // Default to local development URLs
        let base_share_url = local_share_url.unwrap_or_else(|| "http://localhost:3000/join".to_string());
        let base_ws_url = local_ws_url.unwrap_or_else(|| "ws://localhost:8080/ws".to_string());
        (
            format!("{}/{}", base_share_url, session_code),
            format!("{}/{}", base_ws_url, session_code)
        )
    } else {
        // Production URLs (currently non-functional, but preserved for future use)
        (
            format!("https://claudia.app/join/{}", session_code),
            format!("wss://claudia.app/ws/{}", session_code)
        )
    };
    
    let collaborative_session = CollaborativeSession {
        id: Uuid::new_v4().to_string(),
        session_id: session_id.clone(),
        project_path,
        session_code,
        share_url,
        ws_url,
        owner_id: "current-user".to_string(), // In real app, get from auth
        created_at: chrono::Utc::now().timestamp(),
        collaborators: vec![
            Collaborator {
                id: "current-user".to_string(),
                name: "You".to_string(),
                email: "<EMAIL>".to_string(),
                avatar: None,
                role: CollaboratorRole::Owner,
                status: CollaboratorStatus::Online,
                cursor: None,
                color: "#3B82F6".to_string(),
                joined_at: chrono::Utc::now().timestamp(),
            }
        ],
    };
    
    let mut sessions = state.sessions.write().await;
    sessions.insert(session_id, collaborative_session.clone());
    
    Ok(collaborative_session)
}

#[tauri::command]
pub async fn stop_collaborative_session(
    session_id: String,
    state: State<'_, CollaborationState>,
) -> Result<(), String> {
    let mut sessions = state.sessions.write().await;
    sessions.remove(&session_id);
    
    let mut connections = state.connections.write().await;
    connections.remove(&session_id);
    
    Ok(())
}

#[tauri::command]
pub async fn invite_collaborator(
    session_id: String,
    email: String,
    role: String,
    _state: State<'_, CollaborationState>,
) -> Result<(), String> {
    let role = match role.as_str() {
        "editor" => CollaboratorRole::Editor,
        "viewer" => CollaboratorRole::Viewer,
        _ => return Err("Invalid role".to_string()),
    };
    
    // In a real implementation, this would:
    // 1. Send an email invitation
    // 2. Create an invitation record in the database
    // 3. Generate a unique invitation token
    
    // For now, we'll just simulate the invitation
    println!("Inviting {} as {:?} to session {}", email, role, session_id);
    
    Ok(())
}

#[tauri::command]
pub async fn remove_collaborator(
    session_id: String,
    collaborator_id: String,
    state: State<'_, CollaborationState>,
) -> Result<(), String> {
    let mut sessions = state.sessions.write().await;
    
    if let Some(session) = sessions.get_mut(&session_id) {
        session.collaborators.retain(|c| c.id != collaborator_id);
        Ok(())
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn update_cursor_position(
    session_id: String,
    user_id: String,
    line: u32,
    column: u32,
    file: String,
    state: State<'_, CollaborationState>,
) -> Result<(), String> {
    let mut sessions = state.sessions.write().await;
    
    if let Some(session) = sessions.get_mut(&session_id) {
        if let Some(collaborator) = session.collaborators.iter_mut().find(|c| c.id == user_id) {
            collaborator.cursor = Some(CursorPosition { line, column, file });
        }
        Ok(())
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn get_session_collaborators(
    session_id: String,
    state: State<'_, CollaborationState>,
) -> Result<Vec<Collaborator>, String> {
    let sessions = state.sessions.read().await;
    
    if let Some(session) = sessions.get(&session_id) {
        Ok(session.collaborators.clone())
    } else {
        Err("Session not found".to_string())
    }
}

#[tauri::command]
pub async fn join_collaborative_session(
    session_code: String,
    user_name: String,
    user_email: String,
    state: State<'_, CollaborationState>,
) -> Result<CollaborativeSession, String> {
    let mut sessions = state.sessions.write().await;
    
    // Find session by code
    let session = sessions
        .values_mut()
        .find(|s| s.session_code == session_code)
        .ok_or("Invalid session code")?;
    
    // Add new collaborator
    let new_collaborator = Collaborator {
        id: Uuid::new_v4().to_string(),
        name: user_name,
        email: user_email,
        avatar: None,
        role: CollaboratorRole::Viewer, // Default role for new joiners
        status: CollaboratorStatus::Online,
        cursor: None,
        color: generate_user_color(),
        joined_at: chrono::Utc::now().timestamp(),
    };
    
    session.collaborators.push(new_collaborator);
    
    Ok(session.clone())
}

#[tauri::command]
pub async fn get_session_permissions(
    session_id: String,
    user_id: String,
    state: State<'_, CollaborationState>,
) -> Result<SessionPermissions, String> {
    let sessions = state.sessions.read().await;
    
    if let Some(session) = sessions.get(&session_id) {
        if let Some(collaborator) = session.collaborators.iter().find(|c| c.id == user_id) {
            let permissions = match collaborator.role {
                CollaboratorRole::Owner => SessionPermissions {
                    can_edit: true,
                    can_invite: true,
                    can_remove: true,
                    can_chat: true,
                    can_voice: true,
                },
                CollaboratorRole::Editor => SessionPermissions {
                    can_edit: true,
                    can_invite: false,
                    can_remove: false,
                    can_chat: true,
                    can_voice: true,
                },
                CollaboratorRole::Viewer => SessionPermissions {
                    can_edit: false,
                    can_invite: false,
                    can_remove: false,
                    can_chat: true,
                    can_voice: false,
                },
            };
            Ok(permissions)
        } else {
            Err("User not found in session".to_string())
        }
    } else {
        Err("Session not found".to_string())
    }
}

// Helper functions
fn generate_session_code() -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    let mut rng = rand::thread_rng();
    
    (0..6)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

fn generate_user_color() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    let colors = vec![
        "#EF4444", "#F59E0B", "#10B981", "#3B82F6", 
        "#8B5CF6", "#EC4899", "#06B6D4", "#6366F1"
    ];
    colors[rng.gen_range(0..colors.len())].to_string()
}