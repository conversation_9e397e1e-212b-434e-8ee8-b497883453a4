use anyhow::{Context, Result};
use dirs;
use log::{error, info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::process::Command;
use std::sync::{Arc, Mutex};
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tauri::AppHandle;
use tokio::time::sleep;
use sysinfo::System;
use thiserror::Error;
use regex::Regex;

/// Helper function to create a std::process::Command with proper environment variables
/// This ensures commands like <PERSON> can find Node.js and other dependencies
fn create_command_with_env(program: &str) -> Command {
    crate::claude_binary::create_command_with_env(program)
}

/// Finds the full path to the claude binary
/// This is necessary because macOS apps have a limited PATH environment
fn find_claude_binary(app_handle: &AppHandle) -> Result<String> {
    crate::claude_binary::find_claude_binary(app_handle).map_err(|e| anyhow::anyhow!(e))
}

/// Represents an MCP server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPServer {
    /// Server name/identifier
    pub name: String,
    /// Transport type: "stdio" or "sse"
    pub transport: String,
    /// Command to execute (for stdio)
    pub command: Option<String>,
    /// Command arguments (for stdio)
    pub args: Vec<String>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// URL endpoint (for SSE)
    pub url: Option<String>,
    /// Configuration scope: "local", "project", or "user"
    pub scope: String,
    /// Whether the server is currently active
    pub is_active: bool,
    /// Server status
    pub status: ServerStatus,
}

/// Server status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerStatus {
    /// Whether the server is running
    pub running: bool,
    /// Last error message if any
    pub error: Option<String>,
    /// Last checked timestamp
    pub last_checked: Option<u64>,
}

/// MCP configuration for project scope (.mcp.json)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPProjectConfig {
    #[serde(rename = "mcpServers")]
    pub mcp_servers: HashMap<String, MCPServerConfig>,
}

/// Individual server configuration in .mcp.json
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPServerConfig {
    pub command: String,
    #[serde(default)]
    pub args: Vec<String>,
    #[serde(default)]
    pub env: HashMap<String, String>,
}

/// Result of adding a server
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddServerResult {
    pub success: bool,
    pub message: String,
    pub server_name: Option<String>,
}

/// Import result for multiple servers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportResult {
    pub imported_count: u32,
    pub failed_count: u32,
    pub servers: Vec<ImportServerResult>,
}

/// Result for individual server import
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImportServerResult {
    pub name: String,
    pub success: bool,
    pub error: Option<String>,
}

/// Executes a claude mcp command
fn execute_claude_mcp_command(app_handle: &AppHandle, args: Vec<&str>) -> Result<String> {
    info!("Executing claude mcp command with args: {:?}", args);

    let claude_path = find_claude_binary(app_handle)?;
    let mut cmd = create_command_with_env(&claude_path);
    cmd.arg("mcp");
    for arg in args {
        cmd.arg(arg);
    }

    let output = cmd.output().context("Failed to execute claude command")?;

    if output.status.success() {
        Ok(String::from_utf8_lossy(&output.stdout).to_string())
    } else {
        let stderr = String::from_utf8_lossy(&output.stderr).to_string();
        Err(anyhow::anyhow!("Command failed: {}", stderr))
    }
}

/// Adds a new MCP server
#[tauri::command]
pub async fn mcp_add(
    app: AppHandle,
    name: String,
    transport: String,
    command: Option<String>,
    args: Vec<String>,
    env: HashMap<String, String>,
    url: Option<String>,
    scope: String,
) -> Result<AddServerResult, String> {
    info!("Adding MCP server: {} with transport: {}", name, transport);

    // Prepare owned strings for environment variables
    let env_args: Vec<String> = env
        .iter()
        .map(|(key, value)| format!("{}={}", key, value))
        .collect();

    let mut cmd_args = vec!["add"];

    // Add scope flag
    cmd_args.push("-s");
    cmd_args.push(&scope);

    // Add transport flag for SSE
    if transport == "sse" {
        cmd_args.push("--transport");
        cmd_args.push("sse");
    }

    // Add environment variables
    for (i, _) in env.iter().enumerate() {
        cmd_args.push("-e");
        cmd_args.push(&env_args[i]);
    }

    // Add name
    cmd_args.push(&name);

    // Add command/URL based on transport
    if transport == "stdio" {
        if let Some(cmd) = &command {
            // Add "--" separator before command to prevent argument parsing issues
            if !args.is_empty() || cmd.contains('-') {
                cmd_args.push("--");
            }
            cmd_args.push(cmd);
            // Add arguments
            for arg in &args {
                cmd_args.push(arg);
            }
        } else {
            return Ok(AddServerResult {
                success: false,
                message: "Command is required for stdio transport".to_string(),
                server_name: None,
            });
        }
    } else if transport == "sse" {
        if let Some(url_str) = &url {
            cmd_args.push(url_str);
        } else {
            return Ok(AddServerResult {
                success: false,
                message: "URL is required for SSE transport".to_string(),
                server_name: None,
            });
        }
    }

    match execute_claude_mcp_command(&app, cmd_args) {
        Ok(output) => {
            info!("Successfully added MCP server: {}", name);
            Ok(AddServerResult {
                success: true,
                message: output.trim().to_string(),
                server_name: Some(name),
            })
        }
        Err(e) => {
            error!("Failed to add MCP server: {}", e);
            Ok(AddServerResult {
                success: false,
                message: e.to_string(),
                server_name: None,
            })
        }
    }
}

/// Lists all configured MCP servers
#[tauri::command]
pub async fn mcp_list(app: AppHandle) -> Result<Vec<MCPServer>, String> {
    info!("Listing MCP servers");

    match execute_claude_mcp_command(&app, vec!["list"]) {
        Ok(output) => {
            info!("Raw output from 'claude mcp list': {:?}", output);
            let trimmed = output.trim();
            info!("Trimmed output: {:?}", trimmed);

            // Check if no servers are configured
            if trimmed.contains("No MCP servers configured") || trimmed.is_empty() {
                info!("No servers found - empty or 'No MCP servers' message");
                return Ok(vec![]);
            }

            // Parse the text output, handling multi-line commands
            let mut servers = Vec::new();
            let lines: Vec<&str> = trimmed.lines().collect();
            info!("Total lines in output: {}", lines.len());
            for (idx, line) in lines.iter().enumerate() {
                info!("Line {}: {:?}", idx, line);
            }

            let mut i = 0;

            while i < lines.len() {
                let line = lines[i];
                info!("Processing line {}: {:?}", i, line);

                // Check if this line starts a new server entry
                if let Some(colon_pos) = line.find(':') {
                    info!("Found colon at position {} in line: {:?}", colon_pos, line);
                    // Make sure this is a server name line (not part of a path)
                    // Server names typically don't contain '/' or '\'
                    let potential_name = line[..colon_pos].trim();
                    info!("Potential server name: {:?}", potential_name);

                    if !potential_name.contains('/') && !potential_name.contains('\\') {
                        info!("Valid server name detected: {:?}", potential_name);
                        let name = potential_name.to_string();
                        let mut command_parts = vec![line[colon_pos + 1..].trim().to_string()];
                        info!("Initial command part: {:?}", command_parts[0]);

                        // Check if command continues on next lines
                        i += 1;
                        while i < lines.len() {
                            let next_line = lines[i];
                            info!("Checking next line {} for continuation: {:?}", i, next_line);

                            // If the next line starts with a server name pattern, break
                            if next_line.contains(':') {
                                let potential_next_name =
                                    next_line.split(':').next().unwrap_or("").trim();
                                info!(
                                    "Found colon in next line, potential name: {:?}",
                                    potential_next_name
                                );
                                if !potential_next_name.is_empty()
                                    && !potential_next_name.contains('/')
                                    && !potential_next_name.contains('\\')
                                {
                                    info!("Next line is a new server, breaking");
                                    break;
                                }
                            }
                            // Otherwise, this line is a continuation of the command
                            info!("Line {} is a continuation", i);
                            command_parts.push(next_line.trim().to_string());
                            i += 1;
                        }

                        // Join all command parts
                        let full_command = command_parts.join(" ");
                        info!("Full command for server '{}': {:?}", name, full_command);

                        // For now, we'll create a basic server entry
                        servers.push(MCPServer {
                            name: name.clone(),
                            transport: "stdio".to_string(), // Default assumption
                            command: Some(full_command),
                            args: vec![],
                            env: HashMap::new(),
                            url: None,
                            scope: "local".to_string(), // Default assumption
                            is_active: false,
                            status: ServerStatus {
                                running: false,
                                error: None,
                                last_checked: None,
                            },
                        });
                        info!("Added server: {:?}", name);

                        continue;
                    } else {
                        info!("Skipping line - name contains path separators");
                    }
                } else {
                    info!("No colon found in line {}", i);
                }

                i += 1;
            }

            info!("Found {} MCP servers total", servers.len());
            for (idx, server) in servers.iter().enumerate() {
                info!(
                    "Server {}: name='{}', command={:?}",
                    idx, server.name, server.command
                );
            }
            Ok(servers)
        }
        Err(e) => {
            error!("Failed to list MCP servers: {}", e);
            Err(e.to_string())
        }
    }
}

/// Gets details for a specific MCP server
#[tauri::command]
pub async fn mcp_get(app: AppHandle, name: String) -> Result<MCPServer, String> {
    info!("Getting MCP server details for: {}", name);

    match execute_claude_mcp_command(&app, vec!["get", &name]) {
        Ok(output) => {
            // Parse the structured text output
            let mut scope = "local".to_string();
            let mut transport = "stdio".to_string();
            let mut command = None;
            let mut args = vec![];
            let env = HashMap::new();
            let mut url = None;

            for line in output.lines() {
                let line = line.trim();

                if line.starts_with("Scope:") {
                    let scope_part = line.replace("Scope:", "").trim().to_string();
                    if scope_part.to_lowercase().contains("local") {
                        scope = "local".to_string();
                    } else if scope_part.to_lowercase().contains("project") {
                        scope = "project".to_string();
                    } else if scope_part.to_lowercase().contains("user")
                        || scope_part.to_lowercase().contains("global")
                    {
                        scope = "user".to_string();
                    }
                } else if line.starts_with("Type:") {
                    transport = line.replace("Type:", "").trim().to_string();
                } else if line.starts_with("Command:") {
                    command = Some(line.replace("Command:", "").trim().to_string());
                } else if line.starts_with("Args:") {
                    let args_str = line.replace("Args:", "").trim().to_string();
                    if !args_str.is_empty() {
                        args = args_str.split_whitespace().map(|s| s.to_string()).collect();
                    }
                } else if line.starts_with("URL:") {
                    url = Some(line.replace("URL:", "").trim().to_string());
                } else if line.starts_with("Environment:") {
                    // TODO: Parse environment variables if they're listed
                    // For now, we'll leave it empty
                }
            }

            Ok(MCPServer {
                name,
                transport,
                command,
                args,
                env,
                url,
                scope,
                is_active: false,
                status: ServerStatus {
                    running: false,
                    error: None,
                    last_checked: None,
                },
            })
        }
        Err(e) => {
            error!("Failed to get MCP server: {}", e);
            Err(e.to_string())
        }
    }
}

/// Removes an MCP server
#[tauri::command]
pub async fn mcp_remove(app: AppHandle, name: String) -> Result<String, String> {
    info!("Removing MCP server: {}", name);

    match execute_claude_mcp_command(&app, vec!["remove", &name]) {
        Ok(output) => {
            info!("Successfully removed MCP server: {}", name);
            Ok(output.trim().to_string())
        }
        Err(e) => {
            error!("Failed to remove MCP server: {}", e);
            Err(e.to_string())
        }
    }
}

/// Adds an MCP server from JSON configuration
#[tauri::command]
pub async fn mcp_add_json(
    app: AppHandle,
    name: String,
    json_config: String,
    scope: String,
) -> Result<AddServerResult, String> {
    info!(
        "Adding MCP server from JSON: {} with scope: {}",
        name, scope
    );

    // Build command args
    let mut cmd_args = vec!["add-json", &name, &json_config];

    // Add scope flag
    let scope_flag = "-s";
    cmd_args.push(scope_flag);
    cmd_args.push(&scope);

    match execute_claude_mcp_command(&app, cmd_args) {
        Ok(output) => {
            info!("Successfully added MCP server from JSON: {}", name);
            Ok(AddServerResult {
                success: true,
                message: output.trim().to_string(),
                server_name: Some(name),
            })
        }
        Err(e) => {
            error!("Failed to add MCP server from JSON: {}", e);
            Ok(AddServerResult {
                success: false,
                message: e.to_string(),
                server_name: None,
            })
        }
    }
}

/// Imports MCP servers from Claude Desktop
#[tauri::command]
pub async fn mcp_add_from_claude_desktop(
    app: AppHandle,
    scope: String,
) -> Result<ImportResult, String> {
    info!(
        "Importing MCP servers from Claude Desktop with scope: {}",
        scope
    );

    // Get Claude Desktop config path based on platform
    let config_path = if cfg!(target_os = "macos") {
        dirs::home_dir()
            .ok_or_else(|| "Could not find home directory".to_string())?
            .join("Library")
            .join("Application Support")
            .join("Claude")
            .join("claude_desktop_config.json")
    } else if cfg!(target_os = "linux") {
        // For WSL/Linux, check common locations
        dirs::config_dir()
            .ok_or_else(|| "Could not find config directory".to_string())?
            .join("Claude")
            .join("claude_desktop_config.json")
    } else {
        return Err(
            "Import from Claude Desktop is only supported on macOS and Linux/WSL".to_string(),
        );
    };

    // Check if config file exists
    if !config_path.exists() {
        return Err(
            "Claude Desktop configuration not found. Make sure Claude Desktop is installed."
                .to_string(),
        );
    }

    // Read and parse the config file
    let config_content = fs::read_to_string(&config_path)
        .map_err(|e| format!("Failed to read Claude Desktop config: {}", e))?;

    let config: serde_json::Value = serde_json::from_str(&config_content)
        .map_err(|e| format!("Failed to parse Claude Desktop config: {}", e))?;

    // Extract MCP servers
    let mcp_servers = config
        .get("mcpServers")
        .and_then(|v| v.as_object())
        .ok_or_else(|| "No MCP servers found in Claude Desktop config".to_string())?;

    let mut imported_count = 0;
    let mut failed_count = 0;
    let mut server_results = Vec::new();

    // Import each server using add-json
    for (name, server_config) in mcp_servers {
        info!("Importing server: {}", name);

        // Convert Claude Desktop format to add-json format
        let mut json_config = serde_json::Map::new();

        // All Claude Desktop servers are stdio type
        json_config.insert(
            "type".to_string(),
            serde_json::Value::String("stdio".to_string()),
        );

        // Add command
        if let Some(command) = server_config.get("command").and_then(|v| v.as_str()) {
            json_config.insert(
                "command".to_string(),
                serde_json::Value::String(command.to_string()),
            );
        } else {
            failed_count += 1;
            server_results.push(ImportServerResult {
                name: name.clone(),
                success: false,
                error: Some("Missing command field".to_string()),
            });
            continue;
        }

        // Add args if present
        if let Some(args) = server_config.get("args").and_then(|v| v.as_array()) {
            json_config.insert("args".to_string(), args.clone().into());
        } else {
            json_config.insert("args".to_string(), serde_json::Value::Array(vec![]));
        }

        // Add env if present
        if let Some(env) = server_config.get("env").and_then(|v| v.as_object()) {
            json_config.insert("env".to_string(), env.clone().into());
        } else {
            json_config.insert(
                "env".to_string(),
                serde_json::Value::Object(serde_json::Map::new()),
            );
        }

        // Convert to JSON string
        let json_str = serde_json::to_string(&json_config)
            .map_err(|e| format!("Failed to serialize config for {}: {}", name, e))?;

        // Call add-json command
        match mcp_add_json(app.clone(), name.clone(), json_str, scope.clone()).await {
            Ok(result) => {
                if result.success {
                    imported_count += 1;
                    server_results.push(ImportServerResult {
                        name: name.clone(),
                        success: true,
                        error: None,
                    });
                    info!("Successfully imported server: {}", name);
                } else {
                    failed_count += 1;
                    let error_msg = result.message.clone();
                    server_results.push(ImportServerResult {
                        name: name.clone(),
                        success: false,
                        error: Some(result.message),
                    });
                    error!("Failed to import server {}: {}", name, error_msg);
                }
            }
            Err(e) => {
                failed_count += 1;
                let error_msg = e.clone();
                server_results.push(ImportServerResult {
                    name: name.clone(),
                    success: false,
                    error: Some(e),
                });
                error!("Error importing server {}: {}", name, error_msg);
            }
        }
    }

    info!(
        "Import complete: {} imported, {} failed",
        imported_count, failed_count
    );

    Ok(ImportResult {
        imported_count,
        failed_count,
        servers: server_results,
    })
}

/// Starts Claude Code as an MCP server
#[tauri::command]
pub async fn mcp_serve(app: AppHandle) -> Result<String, String> {
    info!("Starting Claude Code as MCP server");

    // Start the server in a separate process
    let claude_path = match find_claude_binary(&app) {
        Ok(path) => path,
        Err(e) => {
            error!("Failed to find claude binary: {}", e);
            return Err(e.to_string());
        }
    };

    let mut cmd = create_command_with_env(&claude_path);
    cmd.arg("mcp").arg("serve");

    match cmd.spawn() {
        Ok(_) => {
            info!("Successfully started Claude Code MCP server");
            Ok("Claude Code MCP server started".to_string())
        }
        Err(e) => {
            error!("Failed to start MCP server: {}", e);
            Err(e.to_string())
        }
    }
}

/// Tests connection to an MCP server
#[tauri::command]
pub async fn mcp_test_connection(app: AppHandle, name: String) -> Result<String, String> {
    info!("Testing connection to MCP server: {}", name);

    // For now, we'll use the get command to test if the server exists
    match execute_claude_mcp_command(&app, vec!["get", &name]) {
        Ok(_) => Ok(format!("Connection to {} successful", name)),
        Err(e) => Err(e.to_string()),
    }
}

/// Resets project-scoped server approval choices
#[tauri::command]
pub async fn mcp_reset_project_choices(app: AppHandle) -> Result<String, String> {
    info!("Resetting MCP project choices");

    match execute_claude_mcp_command(&app, vec!["reset-project-choices"]) {
        Ok(output) => {
            info!("Successfully reset MCP project choices");
            Ok(output.trim().to_string())
        }
        Err(e) => {
            error!("Failed to reset project choices: {}", e);
            Err(e.to_string())
        }
    }
}

/// Security-related errors
#[derive(Error, Debug)]
pub enum SecurityError {
    #[error("Invalid server name: {0}")]
    InvalidServerName(String),
    #[error("Invalid command: {0}")]
    InvalidCommand(String),
    #[error("Invalid environment variable: {0}")]
    InvalidEnvironmentVariable(String),
    #[error("Process monitoring failed: {0}")]
    ProcessMonitoringFailed(String),
}

/// Performance metrics for monitoring
#[derive(Debug, Clone, Default, Serialize)]
pub struct PerformanceMetrics {
    pub cache_hits: u64,
    pub cache_misses: u64,
    pub process_checks: u64,
    pub security_violations: u64,
    pub last_refresh_duration: Duration,
}

/// Thread-safe cache with proper synchronization
struct SecureStatusCache {
    cache: Arc<Mutex<HashMap<String, (ServerStatus, u64)>>>,
    ttl: Duration,
    metrics: Arc<Mutex<PerformanceMetrics>>,
}

impl SecureStatusCache {
    fn new(ttl: Duration) -> Self {
        Self {
            cache: Arc::new(Mutex::new(HashMap::new())),
            ttl,
            metrics: Arc::new(Mutex::new(PerformanceMetrics::default())),
        }
    }

    fn get(&self, key: &str) -> Option<ServerStatus> {
        let cache = self.cache.lock().unwrap();
        let now = current_timestamp();
        
        if let Some((status, timestamp)) = cache.get(key) {
            if now - timestamp < self.ttl.as_secs() {
                // Cache hit
                if let Ok(mut metrics) = self.metrics.lock() {
                    metrics.cache_hits += 1;
                }
                return Some(status.clone());
            }
        }
        
        // Cache miss
        if let Ok(mut metrics) = self.metrics.lock() {
            metrics.cache_misses += 1;
        }
        None
    }

    fn set(&self, key: String, status: ServerStatus) {
        let mut cache = self.cache.lock().unwrap();
        let now = current_timestamp();
        cache.insert(key, (status, now));
    }

    fn clear(&self) {
        let mut cache = self.cache.lock().unwrap();
        cache.clear();
    }

    fn get_metrics(&self) -> PerformanceMetrics {
        self.metrics.lock().unwrap().clone()
    }
}

/// Secure process monitor using sysinfo
pub struct SecureProcessMonitor {
    system: Arc<Mutex<System>>,
    cache: SecureStatusCache,
}

impl SecureProcessMonitor {
    fn new() -> Self {
        let system = System::new_all();
        Self {
            system: Arc::new(Mutex::new(system)),
            cache: SecureStatusCache::new(Duration::from_secs(300)), // 5 minutes
        }
    }

    /// Validates server name for security
    pub fn validate_server_name(name: &str) -> Result<(), SecurityError> {
        // Server names should be alphanumeric with allowed special chars
        let re = Regex::new(r"^[a-zA-Z0-9_-]+$").unwrap();
        if !re.is_match(name) {
            return Err(SecurityError::InvalidServerName(name.to_string()));
        }
        if name.len() > 100 {
            return Err(SecurityError::InvalidServerName("Name too long".to_string()));
        }
        Ok(())
    }

    /// Validates command for security
    pub fn validate_command(command: &str) -> Result<(), SecurityError> {
        // Basic command validation - no shell metacharacters
        let dangerous_chars = [";", "&", "|", ">", "<", "`", "$", "(", ")"];
        for ch in dangerous_chars {
            if command.contains(ch) {
                return Err(SecurityError::InvalidCommand(
                    format!("Command contains dangerous character: {}", ch)
                ));
            }
        }
        if command.len() > 500 {
            return Err(SecurityError::InvalidCommand("Command too long".to_string()));
        }
        Ok(())
    }

    /// Safely checks if a process is running using sysinfo
    fn is_process_running_secure(&self, command: &str, args: &[String]) -> Result<bool, SecurityError> {
        // Validate inputs
        Self::validate_command(command)?;
        
        let mut system = self.system.lock().unwrap();
        system.refresh_processes();
        
        // Extract executable name from command
        let executable_name = if command == "npx" {
            // For NPX, look for node processes with package name
            "node"
        } else {
            command.split('/').last().unwrap_or(command)
        };
        
        // Search for matching processes
        for (_, process) in system.processes() {
            let process_name = process.name();
            let process_cmd = process.cmd();
            
            // Check if process name matches
            if process_name.contains(executable_name) {
                // For NPX servers, check if arguments contain the package name
                if command == "npx" && !args.is_empty() {
                    let empty_string = String::new();
                    let package_name = args.get(0).unwrap_or(&empty_string);
                    if package_name.starts_with('-') {
                        // Skip flags like -y, get the actual package name
                        if let Some(pkg) = args.get(1) {
                            if process_cmd.iter().any(|arg| arg.contains(pkg)) {
                                return Ok(true);
                            }
                        }
                    } else if process_cmd.iter().any(|arg| arg.contains(package_name)) {
                        return Ok(true);
                    }
                } else {
                    // For other commands, check if arguments match
                    if args.is_empty() || args.iter().any(|arg| {
                        process_cmd.iter().any(|cmd_arg| cmd_arg.contains(arg))
                    }) {
                        return Ok(true);
                    }
                }
            }
        }
        
        Ok(false)
    }
}

/// Global secure process monitor
static PROCESS_MONITOR: std::sync::OnceLock<SecureProcessMonitor> = std::sync::OnceLock::new();

/// Gets the global process monitor instance
fn get_process_monitor() -> &'static SecureProcessMonitor {
    PROCESS_MONITOR.get_or_init(|| SecureProcessMonitor::new())
}

/// Cache TTL in seconds (5 minutes)
const STATUS_CACHE_TTL: u64 = 300;

/// Sanitizes environment variable names and values for safe logging
pub fn sanitize_env_var_for_logging(name: &str, value: &str) -> String {
    // Only show first 3 and last 3 characters for security
    if value.len() > 6 {
        format!("{}: {}***{}", name, &value[..3], &value[value.len()-3..])
    } else {
        format!("{}: ***", name)
    }
}

/// Gets current timestamp in seconds
fn current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_else(|_| Duration::from_secs(0))
        .as_secs()
}

/// Securely checks if a process is running using the process monitor
pub fn is_process_running_secure(command: &str, args: &[String]) -> bool {
    let monitor = get_process_monitor();
    
    match monitor.is_process_running_secure(command, args) {
        Ok(running) => {
            info!("Process check for '{}' with args {:?}: {}", command, args, running);
            running
        }
        Err(e) => {
            error!("Security error during process check: {}", e);
            // Log security violation
            if let Ok(mut metrics) = monitor.cache.metrics.lock() {
                metrics.security_violations += 1;
            }
            false
        }
    }
}

/// Securely checks if a server has required environment variables
pub fn check_environment_dependencies_secure(env: &HashMap<String, String>) -> Option<String> {
    for (key, value) in env {
        // Validate environment variable name
        if let Err(e) = validate_env_var_name(key) {
            warn!("Invalid environment variable name: {}", e);
            continue;
        }
        
        // Skip placeholder values
        if value.starts_with("${") && value.ends_with("}") {
            // Validate and extract environment variable name
            let env_var = &value[2..value.len()-1];
            if let Err(e) = validate_env_var_name(env_var) {
                return Some(format!("Invalid environment variable reference: {}", e));
            }
            
            // Check if the actual environment variable exists
            if std::env::var(env_var).is_err() {
                // Don't expose the actual variable name in error messages
                return Some("Missing required environment variable".to_string());
            }
        }
    }
    None
}

/// Validates environment variable names for security
pub fn validate_env_var_name(name: &str) -> Result<(), SecurityError> {
    // Environment variable names should be alphanumeric with underscores
    let re = Regex::new(r"^[A-Z_][A-Z0-9_]*$").unwrap();
    if !re.is_match(name) {
        return Err(SecurityError::InvalidEnvironmentVariable(name.to_string()));
    }
    if name.len() > 100 {
        return Err(SecurityError::InvalidEnvironmentVariable("Name too long".to_string()));
    }
    Ok(())
}

/// Securely checks the status of a single MCP server
fn check_server_status_secure(server: &MCPServer) -> ServerStatus {
    let now = current_timestamp();
    
    // Validate server name
    if let Err(e) = SecureProcessMonitor::validate_server_name(&server.name) {
        error!("Invalid server name: {}", e);
        return ServerStatus {
            running: false,
            error: Some("Invalid server configuration".to_string()),
            last_checked: Some(now),
        };
    }
    
    // Check environment dependencies first
    if let Some(env_error) = check_environment_dependencies_secure(&server.env) {
        return ServerStatus {
            running: false,
            error: Some(env_error),
            last_checked: Some(now),
        };
    }
    
    // Check if process is running
    let running = if let Some(command) = &server.command {
        // Parse command to separate the actual command from arguments
        let parts: Vec<&str> = command.split_whitespace().collect();
        if let Some(cmd) = parts.get(0) {
            let args: Vec<String> = parts[1..].iter().map(|s| s.to_string()).collect();
            let mut all_args = args;
            all_args.extend(server.args.clone());
            
            is_process_running_secure(cmd, &all_args)
        } else {
            false
        }
    } else {
        false
    };
    
    ServerStatus {
        running,
        error: if running { None } else { Some("Process not running".to_string()) },
        last_checked: Some(now),
    }
}

/// Gets server status from cache or checks if cache is expired (secure version)
fn get_cached_or_check_status_secure(server: &MCPServer) -> ServerStatus {
    let monitor = get_process_monitor();
    
    // Try to get from cache first
    if let Some(status) = monitor.cache.get(&server.name) {
        return status;
    }
    
    // Cache miss or expired, check status
    let start_time = std::time::Instant::now();
    let status = check_server_status_secure(server);
    let duration = start_time.elapsed();
    
    // Update performance metrics
    if let Ok(mut metrics) = monitor.cache.metrics.lock() {
        metrics.process_checks += 1;
        metrics.last_refresh_duration = duration;
    }
    
    // Update cache
    monitor.cache.set(server.name.clone(), status.clone());
    
    status
}

/// Gets the status of MCP servers (secure version)
#[tauri::command]
pub async fn mcp_get_server_status(app: AppHandle) -> Result<HashMap<String, ServerStatus>, String> {
    info!("Getting MCP server status (secure)");
    
    // Get all servers first
    let servers = match mcp_list(app).await {
        Ok(servers) => servers,
        Err(e) => {
            error!("Failed to get server list: {}", e);
            return Err(e);
        }
    };
    
    let mut status_map = HashMap::new();
    
    // Check status for each server
    for server in servers {
        let status = get_cached_or_check_status_secure(&server);
        status_map.insert(server.name.clone(), status);
    }
    
    info!("Retrieved status for {} servers", status_map.len());
    Ok(status_map)
}

/// Forces a refresh of server status (clears cache) - secure version
#[tauri::command]
pub async fn mcp_refresh_server_status(app: AppHandle) -> Result<HashMap<String, ServerStatus>, String> {
    info!("Refreshing MCP server status (clearing cache) - secure");
    
    // Clear cache
    let monitor = get_process_monitor();
    monitor.cache.clear();
    
    // Get fresh status
    mcp_get_server_status(app).await
}

/// Gets performance metrics for MCP server monitoring
#[tauri::command]
pub async fn mcp_get_performance_metrics() -> Result<PerformanceMetrics, String> {
    info!("Getting MCP performance metrics");
    
    let monitor = get_process_monitor();
    let metrics = monitor.cache.get_metrics();
    
    Ok(metrics)
}

/// Background task to periodically refresh server status
pub async fn start_status_refresh_task(app: AppHandle) {
    info!("Starting background server status refresh task");
    
    loop {
        // Wait for 5 minutes
        sleep(Duration::from_secs(STATUS_CACHE_TTL)).await;
        
        // Refresh status in background
        match mcp_refresh_server_status(app.clone()).await {
            Ok(status) => {
                info!("Background refresh completed for {} servers", status.len());
            }
            Err(e) => {
                warn!("Background refresh failed: {}", e);
            }
        }
    }
}

/// Reads .mcp.json from the current project
#[tauri::command]
pub async fn mcp_read_project_config(project_path: String) -> Result<MCPProjectConfig, String> {
    info!("Reading .mcp.json from project: {}", project_path);

    let mcp_json_path = PathBuf::from(&project_path).join(".mcp.json");

    if !mcp_json_path.exists() {
        return Ok(MCPProjectConfig {
            mcp_servers: HashMap::new(),
        });
    }

    match fs::read_to_string(&mcp_json_path) {
        Ok(content) => match serde_json::from_str::<MCPProjectConfig>(&content) {
            Ok(config) => Ok(config),
            Err(e) => {
                error!("Failed to parse .mcp.json: {}", e);
                Err(format!("Failed to parse .mcp.json: {}", e))
            }
        },
        Err(e) => {
            error!("Failed to read .mcp.json: {}", e);
            Err(format!("Failed to read .mcp.json: {}", e))
        }
    }
}

/// Saves .mcp.json to the current project
#[tauri::command]
pub async fn mcp_save_project_config(
    project_path: String,
    config: MCPProjectConfig,
) -> Result<String, String> {
    info!("Saving .mcp.json to project: {}", project_path);

    let mcp_json_path = PathBuf::from(&project_path).join(".mcp.json");

    let json_content = serde_json::to_string_pretty(&config)
        .map_err(|e| format!("Failed to serialize config: {}", e))?;

    fs::write(&mcp_json_path, json_content)
        .map_err(|e| format!("Failed to write .mcp.json: {}", e))?;

    Ok("Project MCP configuration saved".to_string())
}
