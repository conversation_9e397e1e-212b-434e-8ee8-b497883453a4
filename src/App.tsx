import { useEffect, lazy, Suspense } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Loader2 } from "lucide-react";
import { OutputCacheProvider } from "@/lib/outputCache";
import { Button } from "@/components/ui/button";
import { ProjectList } from "@/components/ProjectList";
import { SessionList } from "@/components/SessionList";
import { RunningClaudeSessions } from "@/components/RunningClaudeSessions";
import { Topbar } from "@/components/Topbar";
import { MarkdownEditor } from "@/components/MarkdownEditor";
import { ClaudeFileEditor } from "@/components/ClaudeFileEditor";
import { Settings } from "@/components/Settings";
import { CCAgents } from "@/components/CCAgents";
import { ClaudeCodeSession } from "@/components/ClaudeCodeSession";
import { UsageDashboard } from "@/components/UsageDashboard";
import { MCPManager } from "@/components/MCPManager";
import { NFOCredits } from "@/components/NFOCredits";
import { ClaudeBinaryDialog } from "@/components/ClaudeBinaryDialog";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { LoadingFallback } from "@/components/LoadingFallback";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { SkipLink } from "@/components/SkipLink";
import { useKeyboardNavigation } from "@/hooks/useKeyboardNavigation";
import { useFocusMode } from "@/hooks/useFocusMode";
import { useFeatureContext } from "@/hooks/useFeatureContext";
import { Toaster } from "sonner";
import { DynamicNavigationCards } from "@/components/DynamicNavigationCards";
import { useUIStore } from "@/stores/uiStore";
import { useProjectStore } from "@/stores/projectStore";
import { useAppInitializer } from "@/hooks/useAppInitializer";

// Lazy load heavy components
const SmartTemplates = lazy(() => import("@/components/SmartTemplates").then(m => ({ default: m.SmartTemplates })));
const PerformanceProfiler = lazy(() => import("@/components/PerformanceProfiler").then(m => ({ default: m.PerformanceProfiler })));
const PluginManager = lazy(() => import("@/components/PluginManager").then(m => ({ default: m.PluginManager })));
const AgentSystem = lazy(() => import("@/components/AgentSystemRefactored").then(m => ({ default: m.AgentSystem })));

type View = "welcome" | "projects" | "agents" | "editor" | "settings" | "claude-file-editor" | "claude-code-session" | "usage-dashboard" | "mcp" | "templates" | "performance" | "plugins" | "agent-system";

/**
 * Main App component - Manages the Claude directory browser UI
 */
function App() {
  const initializationStatus = useAppInitializer();
  const {
    view,
    loading,
    error,
    showNFO,
    showClaudeBinaryDialog,
    toast,
    activeClaudeSessionId,
    setView,
    setLoading,
    setShowNFO,
    setShowClaudeBinaryDialog,
    setToast,
    setClaudeStreaming
  } = useUIStore();
  
  const {
    projects,
    selectedProject,
    sessions,
    editingClaudeFile,
    selectedSession,
    fetchProjects,
    selectProject,
    clearSelectedProject,
    selectSession,
    editClaudeFile,
    clearEditingClaudeFile,
  } = useProjectStore();

  // Focus mode and feature context
  const { getVisibleFeatures } = useFocusMode();
  const { suggestedFeatures } = useFeatureContext(view, selectedProject?.path);

  // Keyboard shortcuts
  useKeyboardNavigation([
    {
      key: '1',
      ctrl: true,
      handler: () => setView('agents'),
      description: 'Go to CC Agents'
    },
    {
      key: '2',
      ctrl: true,
      handler: () => setView('projects'),
      description: 'Go to CC Projects'
    },
    {
      key: '3',
      ctrl: true,
      handler: () => setView('templates'),
      description: 'Go to Smart Templates'
    },
    {
      key: '4',
      ctrl: true,
      handler: () => setView('performance'),
      description: 'Go to Performance'
    },
    {
      key: 'h',
      ctrl: true,
      handler: () => setView('welcome'),
      description: 'Go to Home'
    },
    {
      key: 'Escape',
      handler: () => {
        if (showNFO) setShowNFO(false);
        if (showClaudeBinaryDialog) setShowClaudeBinaryDialog(false);
      },
      description: 'Close dialogs'
    }
  ]);

  // Load projects on mount when in projects view
  useEffect(() => {
    if (view === "projects") {
      fetchProjects();
    } else if (view === "welcome") {
      // Reset loading state for welcome view
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [view]);

  // Listen for Claude session selection events
  useEffect(() => {
    const handleSessionSelected = (event: CustomEvent) => {
      const { session } = event.detail;
      selectSession(session);
    };

    const handleClaudeNotFound = () => {
      setShowClaudeBinaryDialog(true);
    };

    window.addEventListener('claude-session-selected', handleSessionSelected as EventListener);
    window.addEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    return () => {
      window.removeEventListener('claude-session-selected', handleSessionSelected as EventListener);
      window.removeEventListener('claude-not-found', handleClaudeNotFound as EventListener);
    };
  }, [selectSession, setShowClaudeBinaryDialog]);

  const handleNewSession = () => {
    selectSession(null);
  };

  const renderContent = () => {
    switch (view) {
      case "welcome":
        return (
          <div className="flex items-center justify-center p-4" style={{ height: "100%" }}>
            <div className="w-full max-w-5xl">
              {/* Welcome Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="mb-12 text-center"
              >
                <h1 className="text-4xl font-bold tracking-tight">
                  <span className="rotating-symbol" aria-hidden="true"></span>
                  Welcome to Claudia Maxed Out
                </h1>
              </motion.div>

              {/* Navigation Cards */}
              <DynamicNavigationCards 
                visibleFeatures={getVisibleFeatures()}
                suggestedFeatures={suggestedFeatures}
                onFeatureClick={(feature) => setView(feature as View)}
              />
            </div>
          </div>
        );

      case "agents":
        return (
          <div className="flex-1 overflow-hidden">
            <CCAgents onBack={() => setView("welcome")} />
          </div>
        );

      case "editor":
        return (
          <div className="flex-1 overflow-hidden">
            <MarkdownEditor onBack={() => setView("welcome")} />
          </div>
        );
      
      case "settings":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Settings onBack={() => setView("welcome")} />
          </div>
        );
      
      case "projects":
        return (
          <div className="flex h-full items-center justify-center p-4 overflow-y-auto">
            <div className="w-full max-w-2xl">
              {/* Header with back button */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="mb-6"
              >
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView("welcome")}
                  className="mb-4"
                >
                  ← Back to Home
                </Button>
                <div className="text-center">
                  <h1 className="text-3xl font-bold tracking-tight">CC Projects</h1>
                  <p className="mt-1 text-sm text-muted-foreground">
                    Browse your Claude Code sessions
                  </p>
                </div>
              </motion.div>

              {/* Error display */}
              {error && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mb-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-xs text-destructive"
                >
                  {error}
                </motion.div>
              )}

              {/* Loading state */}
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                </div>
              )}

              {/* Content */}
              {!loading && (
                <AnimatePresence mode="wait">
                  {selectedProject ? (
                    <motion.div
                      key="sessions"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      <SessionList
                        sessions={sessions}
                        projectPath={selectedProject.path}
                        onBack={clearSelectedProject}
                        onEditClaudeFile={editClaudeFile}
                      />
                    </motion.div>
                  ) : (
                    <motion.div
                      key="projects"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      {/* New session button at the top */}
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Button
                          onClick={handleNewSession}
                          size="default"
                          className="w-full"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          New Claude Code session
                        </Button>
                      </motion.div>

                      {/* Running Claude Sessions */}
                      <RunningClaudeSessions />

                      {/* Project list */}
                      {projects.length > 0 ? (
                        <ProjectList
                          projects={projects}
                          onProjectClick={selectProject}
                        />
                      ) : (
                        <div className="py-8 text-center">
                          <p className="text-sm text-muted-foreground">
                            No projects found in ~/.claude/projects
                          </p>
                        </div>
                      )}
                    </motion.div>
                  )}
                </AnimatePresence>
              )}
            </div>
          </div>
        );
      
      case "claude-file-editor":
        return editingClaudeFile ? (
          <ClaudeFileEditor
            file={editingClaudeFile}
            onBack={clearEditingClaudeFile}
          />
        ) : null;
      
      case "claude-code-session":
        return (
          <ClaudeCodeSession
            session={selectedSession || undefined}
            onBack={() => {
              selectSession(null);
              setView("projects");
            }}
            onStreamingChange={setClaudeStreaming}
          />
        );
      
      case "usage-dashboard":
        return (
          <UsageDashboard onBack={() => setView("welcome")} />
        );
      
      case "mcp":
        return (
          <MCPManager onBack={() => setView("welcome")} />
        );
      
      case "templates":
        return (
          <Suspense fallback={<LoadingFallback />}>
            <SmartTemplates 
              onSelectTemplate={(template) => {
                // Handle template selection
              }}
              onClose={() => setView("welcome")}
            />
          </Suspense>
        );
      
      case "performance":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <PerformanceProfiler 
                sessionId="default" 
                className="flex-1"
              />
            </Suspense>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setView("welcome")}
              className="absolute top-4 left-4"
            >
              ← Back to Home
            </Button>
          </div>
        );
      
      case "plugins":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <PluginManager onClose={() => setView("welcome")} />
            </Suspense>
          </div>
        );
      
      case "agent-system":
        return (
          <div className="flex-1 flex flex-col" style={{ minHeight: 0 }}>
            <Suspense fallback={<LoadingFallback />}>
              <AgentSystem 
                sessionId={activeClaudeSessionId || "demo-session"} 
                projectPath={selectedProject?.path || "/demo/project"}
                onClose={() => setView("welcome")}
              />
            </Suspense>
          </div>
        );
      
      default:
        return null;
    }
  };

  if (initializationStatus === 'initializing') {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (initializationStatus === 'error') {
    return (
      <div className="flex h-screen items-center justify-center p-4 text-center">
        <div>
          <h1 className="text-2xl font-bold text-destructive">Application Error</h1>
          <p className="text-muted-foreground">{error}</p>
          <Button onClick={() => window.location.reload()} className="mt-4">
            Reload Application
          </Button>
        </div>
      </div>
    );
  }
  
  return (
    <ErrorBoundary>
      <OutputCacheProvider>
        <div className="h-screen bg-background flex flex-col">
          <SkipLink href="#main-content">Skip to main content</SkipLink>
          {/* Topbar */}
          <Topbar
            onClaudeClick={() => setView("editor")}
            onSettingsClick={() => setView("settings")}
            onUsageClick={() => setView("usage-dashboard")}
            onPerformanceClick={() => setView("performance")}
            onPluginsClick={() => setView("plugins")}
            onAgentSystemClick={() => setView("agent-system")}
            onMCPClick={() => setView("mcp")}
            onInfoClick={() => setShowNFO(true)}
          />
          
          {/* Main Content */}
          <main id="main-content" className="flex-1 overflow-y-auto" role="main" aria-label="Main content">
            <ErrorBoundary>
              {renderContent()}
            </ErrorBoundary>
          </main>
          
          {/* NFO Credits Modal */}
          {showNFO && <NFOCredits onClose={() => setShowNFO(false)} />}
          
          {/* Claude Binary Dialog */}
          <ClaudeBinaryDialog
            open={showClaudeBinaryDialog}
            onOpenChange={setShowClaudeBinaryDialog}
            onSuccess={() => {
              setToast({ message: "Claude binary path saved successfully", type: "success" });
              // Trigger a refresh of the Claude version check
              window.location.reload();
            }}
            onError={(message) => setToast({ message, type: "error" })}
          />
          
          {/* Toast Container */}
          <ToastContainer>
            {toast && (
              <Toast
                message={toast.message}
                type={toast.type}
                onDismiss={() => setToast(null)}
              />
            )}
          </ToastContainer>
          
          {/* Sonner Toaster */}
          <Toaster
            position="bottom-right"
            richColors
            expand
            closeButton
          />
        </div>
      </OutputCacheProvider>
    </ErrorBoundary>
  );
}

export default App;
