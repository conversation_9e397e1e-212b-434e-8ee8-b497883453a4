import { create } from 'zustand';
import { type Project, type Session, type ClaudeMdFile, api } from '@/lib/api';
import { useUIStore } from './uiStore';

interface ProjectState {
  projects: Project[];
  selectedProject: Project | null;
  sessions: Session[];
  selectedSession: Session | null;
  editingClaudeFile: ClaudeMdFile | null;
  fetchProjects: () => Promise<void>;
  selectProject: (project: Project) => Promise<void>;
  clearSelectedProject: () => void;
  selectSession: (session: Session | null) => void;
  editClaudeFile: (file: ClaudeMdFile) => void;
  clearEditingClaudeFile: () => void;
}

export const useProjectStore = create<ProjectState>((set) => ({
  projects: [],
  selectedProject: null,
  sessions: [],
  selectedSession: null,
  editingClaudeFile: null,

  fetchProjects: async () => {
    try {
      useUIStore.getState().setLoading(true);
      useUIStore.getState().setError(null);
      const projectList = await api.listProjects();
      set({ projects: projectList });
    } catch (err) {
      console.error("Failed to load projects:", err);
      useUIStore.getState().setError("Failed to load projects. Please ensure ~/.claude directory exists.");
    } finally {
      useUIStore.getState().setLoading(false);
    }
  },

  selectProject: async (project: Project) => {
    try {
      useUIStore.getState().setLoading(true);
      useUIStore.getState().setError(null);
      const sessionList = await api.getProjectSessions(project.id);
      set({ selectedProject: project, sessions: sessionList });
    } catch (err) {
      console.error("Failed to load sessions:", err);
      useUIStore.getState().setError("Failed to load sessions for this project.");
    } finally {
      useUIStore.getState().setLoading(false);
    }
  },

  clearSelectedProject: () => {
    set({ selectedProject: null, sessions: [] });
  },

  selectSession: (session: Session | null) => {
    set({ selectedSession: session });
    useUIStore.getState().setView("claude-code-session");
  },

  editClaudeFile: (file: ClaudeMdFile) => {
    set({ editingClaudeFile: file });
    useUIStore.getState().setView("claude-file-editor");
  },

  clearEditingClaudeFile: () => {
    set({ editingClaudeFile: null });
    useUIStore.getState().setView("projects");
  },
}));