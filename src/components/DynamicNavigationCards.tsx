import { motion, AnimatePresence } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bot, 
  FolderCode, 
  FileCode2, 
  Activity, 
  Puzzle,
  Brain,
  Sparkles,
  BarChart3,
  Network,
  ChevronDown,
  Grid
} from 'lucide-react';
import { useFocusMode } from '@/hooks/useFocusMode';
import { useState, useCallback, useMemo, memo } from 'react';
import { cn } from '@/lib/utils';

interface DynamicNavigationCardsProps {
  visibleFeatures: string[];
  suggestedFeatures: string[];
  onFeatureClick: (feature: string) => void;
}

interface FeatureCardInfo {
  id: string;
  name: string;
  icon: React.ReactNode;
  color?: string;
  description?: string;
}

const FEATURE_CARDS: Record<string, FeatureCardInfo> = {
  'agents': {
    id: 'agents',
    name: 'CC Agents',
    icon: <Bot className="h-16 w-16 mb-4 text-primary" />,
    description: 'Create and manage AI agents'
  },
  'projects': {
    id: 'projects',
    name: 'CC Projects',
    icon: <FolderCode className="h-16 w-16 mb-4 text-primary" />,
    description: 'Browse your Claude sessions'
  },
  'templates': {
    id: 'templates',
    name: 'Smart Templates',
    icon: <FileCode2 className="h-16 w-16 mb-4 text-primary" />,
    description: 'AI-powered code templates'
  },
  'performance': {
    id: 'performance',
    name: 'Performance',
    icon: <Activity className="h-16 w-16 mb-4 text-primary" />,
    description: 'Monitor API usage and costs'
  },
  'plugins': {
    id: 'plugins',
    name: 'Plugins',
    icon: <Puzzle className="h-16 w-16 mb-4 text-primary" />,
    description: 'Extend with custom plugins'
  },
  'agent-system': {
    id: 'agent-system',
    name: 'Agent System',
    icon: <Brain className="h-16 w-16 mb-4 text-primary" />,
    description: 'Advanced agent workflows'
  },
  'usage-dashboard': {
    id: 'usage-dashboard',
    name: 'Usage Dashboard',
    icon: <BarChart3 className="h-16 w-16 mb-4 text-primary" />,
    description: 'Track usage and costs'
  },
  'mcp': {
    id: 'mcp',
    name: 'MCP Manager',
    icon: <Network className="h-16 w-16 mb-4 text-primary" />,
    description: 'Manage MCP servers'
  }
};

export const DynamicNavigationCards = memo(function DynamicNavigationCards({ 
  visibleFeatures, 
  suggestedFeatures,
  onFeatureClick 
}: DynamicNavigationCardsProps) {
  const { config } = useFocusMode();
  const [showAllFeatures, setShowAllFeatures] = useState(false);

  // Filter features based on visibility and focus mode
  const displayedFeatures = useMemo(() => {
    return config.enabled && !showAllFeatures
      ? visibleFeatures.filter(f => FEATURE_CARDS[f])
      : Object.keys(FEATURE_CARDS);
  }, [config.enabled, showAllFeatures, visibleFeatures]);

  // Identify which features are suggested
  const getSuggestionBadge = useCallback((featureId: string) => {
    if (!suggestedFeatures.includes(featureId)) return null;
    
    const reasons = {
      'performance': 'Long session',
      'templates': 'Frequently used',
      'agents': 'Project type match',
      'usage-dashboard': 'Monitor costs'
    };
    
    return reasons[featureId as keyof typeof reasons] || 'Recommended';
  }, [suggestedFeatures]);

  const handleFeatureClick = useCallback((featureId: string) => {
    onFeatureClick(featureId);
  }, [onFeatureClick]);

  const handleToggleAllFeatures = useCallback(() => {
    setShowAllFeatures(prev => !prev);
  }, []);

  const totalFeatures = useMemo(() => Object.keys(FEATURE_CARDS).length, []);

  return (
    <div className="w-full">
      {/* Focus Mode Info */}
      {config.enabled && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 text-center"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full">
            <Sparkles className="w-4 h-4 text-primary" />
            <span className="text-sm font-medium">
              Focus Mode Active - Showing {displayedFeatures.length} essential features
            </span>
          </div>
          {displayedFeatures.length < totalFeatures && (
            <Button
              variant="link"
              size="sm"
              onClick={handleToggleAllFeatures}
              className="mt-2"
            >
              {showAllFeatures ? 'Show Less' : 'Show All Features'}
              <ChevronDown className={cn(
                "w-4 h-4 ml-1 transition-transform",
                showAllFeatures && "rotate-180"
              )} />
            </Button>
          )}
        </motion.div>
      )}

      {/* Navigation Grid */}
      <nav 
        className={cn(
          "grid gap-6 max-w-6xl mx-auto",
          displayedFeatures.length <= 3 
            ? "grid-cols-1 md:grid-cols-3" 
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        )} 
        role="navigation" 
        aria-label="Main navigation"
      >
        <AnimatePresence mode="popLayout">
          {displayedFeatures.map((featureId, index) => {
            const feature = FEATURE_CARDS[featureId];
            if (!feature) return null;
            
            const suggestionReason = getSuggestionBadge(featureId);
            const isSuggested = suggestedFeatures.includes(featureId);
            
            return (
              <motion.div
                key={featureId}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                layout
              >
                <Card 
                  className={cn(
                    "h-64 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-lg border shimmer-hover trailing-border relative overflow-hidden",
                    isSuggested && "border-primary/50 bg-primary/5"
                  )}
                  onClick={() => handleFeatureClick(featureId)}
                >
                  {/* Suggestion Badge */}
                  {suggestionReason && (
                    <motion.div
                      initial={{ x: 100, opacity: 0 }}
                      animate={{ x: 0, opacity: 1 }}
                      transition={{ delay: 0.2 + index * 0.05 }}
                      className="absolute top-2 right-2"
                    >
                      <Badge variant="secondary" className="text-xs">
                        <Sparkles className="w-3 h-3 mr-1" />
                        {suggestionReason}
                      </Badge>
                    </motion.div>
                  )}
                  
                  <div className="h-full flex flex-col items-center justify-center p-8">
                    {feature.icon}
                    <h2 className="text-xl font-semibold">{feature.name}</h2>
                    {feature.description && (
                      <p className="text-sm text-muted-foreground mt-2 text-center">
                        {feature.description}
                      </p>
                    )}
                  </div>
                  
                  {/* Glow effect for suggested features */}
                  {isSuggested && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent pointer-events-none"
                      animate={{
                        opacity: [0.3, 0.6, 0.3],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />
                  )}
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </nav>

      {/* Hidden Features Indicator */}
      {config.enabled && !showAllFeatures && displayedFeatures.length < totalFeatures && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="text-center mt-6"
        >
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleAllFeatures}
            className="gap-2"
          >
            <Grid className="w-4 h-4" />
            {totalFeatures - displayedFeatures.length} more features available
          </Button>
        </motion.div>
      )}
    </div>
  );
}