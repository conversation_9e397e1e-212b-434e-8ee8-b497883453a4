import { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Popover } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mic, 
  MicOff, 
  Volume2, 
  Settings2,
  Zap,
  AlertCircle,
  CheckCircle2,
  Info
} from 'lucide-react';
import { useFeatureFlags } from '@/lib/featureFlags';

interface VoiceControlProps {
  onCommand: (command: string) => void;
  isLoading?: boolean;
  className?: string;
}

interface VoiceCommand {
  pattern: RegExp;
  action: string;
  description: string;
}

const VOICE_COMMANDS: VoiceCommand[] = [
  { pattern: /^(run|execute) code review$/i, action: 'code-review', description: 'Run code review' },
  { pattern: /^(show|open) timeline$/i, action: 'show-timeline', description: 'Show timeline' },
  { pattern: /^(show|open) settings$/i, action: 'show-settings', description: 'Show settings' },
  { pattern: /^(create|make) checkpoint$/i, action: 'create-checkpoint', description: 'Create checkpoint' },
  { pattern: /^(send|submit) prompt (.+)$/i, action: 'send-prompt', description: 'Send prompt' },
  { pattern: /^(cancel|stop) execution$/i, action: 'cancel-execution', description: 'Cancel execution' },
  { pattern: /^(scroll|go) to top$/i, action: 'scroll-top', description: 'Scroll to top' },
  { pattern: /^(scroll|go) to bottom$/i, action: 'scroll-bottom', description: 'Scroll to bottom' },
  { pattern: /^(switch|change) to opus$/i, action: 'switch-opus', description: 'Switch to Opus model' },
  { pattern: /^(switch|change) to sonnet$/i, action: 'switch-sonnet', description: 'Switch to Sonnet model' },
];

export function VoiceControl({ onCommand, isLoading = false, className }: VoiceControlProps) {
  const { flags } = useFeatureFlags();
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [interimTranscript, setInterimTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);
  const [browserInfo, setBrowserInfo] = useState<string>('');
  const [language, setLanguage] = useState('en-US');
  const [continuous, setContinuous] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [showCommands, setShowCommands] = useState(false);
  const [lastCommand, setLastCommand] = useState<string | null>(null);
  const [showBrowserWarning, setShowBrowserWarning] = useState(false);
  
  const recognitionRef = useRef<any>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  useEffect(() => {
    // Check if feature is enabled
    if (!flags.voiceControl.enabled) {
      setIsSupported(false);
      return;
    }

    // Detect browser
    const userAgent = navigator.userAgent.toLowerCase();
    let browser = 'Unknown browser';
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) {
      browser = 'Google Chrome';
    } else if (userAgent.includes('safari') && !userAgent.includes('chrome')) {
      browser = 'Safari';
    } else if (userAgent.includes('firefox')) {
      browser = 'Firefox';
    } else if (userAgent.includes('edg')) {
      browser = 'Microsoft Edge';
    }
    setBrowserInfo(browser);

    // Check if Web Speech API is supported
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    if (!SpeechRecognition) {
      setIsSupported(false);
      setShowBrowserWarning(flags.voiceControl.showUnsupportedWarning);
      return;
    }

    // Initialize speech recognition
    const recognition = new SpeechRecognition();
    recognition.continuous = continuous;
    recognition.interimResults = true;
    recognition.lang = language;

    recognition.onstart = () => {
      setIsListening(true);
      setError(null);
      playSound('start');
    };

    recognition.onresult = (event: any) => {
      let interimTranscript = '';
      let finalTranscript = '';

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript + ' ';
        } else {
          interimTranscript += transcript;
        }
      }

      setInterimTranscript(interimTranscript);
      
      if (finalTranscript) {
        const trimmedTranscript = finalTranscript.trim();
        setTranscript(trimmedTranscript);
        processCommand(trimmedTranscript);
      }
    };

    recognition.onerror = (event: any) => {
      setError(`Error: ${event.error}`);
      setIsListening(false);
      playSound('error');
    };

    recognition.onend = () => {
      setIsListening(false);
      if (continuous && !error) {
        // Restart if continuous mode is enabled
        setTimeout(() => {
          if (continuous) {
            recognition.start();
          }
        }, 100);
      } else {
        playSound('end');
      }
    };

    recognitionRef.current = recognition;

    // Initialize audio context for sound effects
    if (soundEnabled) {
      audioContextRef.current = new AudioContext();
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [language, continuous, soundEnabled, flags]);

  const playSound = useCallback((type: 'start' | 'end' | 'success' | 'error') => {
    if (!soundEnabled || !audioContextRef.current) return;

    const oscillator = audioContextRef.current.createOscillator();
    const gainNode = audioContextRef.current.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContextRef.current.destination);
    
    gainNode.gain.setValueAtTime(0.1, audioContextRef.current.currentTime);
    
    switch (type) {
      case 'start':
        oscillator.frequency.setValueAtTime(800, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1200, audioContextRef.current.currentTime + 0.1);
        break;
      case 'end':
        oscillator.frequency.setValueAtTime(1200, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(800, audioContextRef.current.currentTime + 0.1);
        break;
      case 'success':
        oscillator.frequency.setValueAtTime(1000, audioContextRef.current.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(1500, audioContextRef.current.currentTime + 0.1);
        break;
      case 'error':
        oscillator.frequency.setValueAtTime(400, audioContextRef.current.currentTime);
        break;
    }
    
    oscillator.start(audioContextRef.current.currentTime);
    oscillator.stop(audioContextRef.current.currentTime + 0.1);
  }, [soundEnabled]);

  const processCommand = useCallback((text: string) => {
    for (const command of VOICE_COMMANDS) {
      const match = text.match(command.pattern);
      if (match) {
        setLastCommand(command.description);
        playSound('success');
        
        if (command.action === 'send-prompt' && match[2]) {
          onCommand(`${command.action}:${match[2]}`);
        } else {
          onCommand(command.action);
        }
        
        // Clear transcript after processing
        setTimeout(() => {
          setTranscript('');
          setInterimTranscript('');
        }, 1000);
        
        return;
      }
    }
    
    // No command matched
    setError('Command not recognized');
    playSound('error');
  }, [onCommand, playSound]);

  const toggleListening = useCallback(() => {
    if (!recognitionRef.current) return;

    if (isListening) {
      recognitionRef.current.stop();
    } else {
      setTranscript('');
      setInterimTranscript('');
      setError(null);
      recognitionRef.current.start();
    }
  }, [isListening]);

  if (!flags.voiceControl.enabled) {
    return null; // Feature disabled
  }

  if (!isSupported) {
    return (
      <div className={className}>
        <Badge 
          variant="secondary" 
          className="gap-1 cursor-pointer"
          onClick={() => setShowBrowserWarning(!showBrowserWarning)}
        >
          <AlertCircle className="w-3 h-3" />
          Voice not supported
        </Badge>
        
        <AnimatePresence>
          {showBrowserWarning && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full mt-2 left-0 right-0 z-50 max-w-md"
            >
              <Alert>
                <Info className="h-4 w-4" />
                <AlertTitle>Voice Control Not Supported</AlertTitle>
                <AlertDescription className="mt-2">
                  <p className="mb-2">
                    Your browser ({browserInfo}) doesn't support the Web Speech API required for voice control.
                  </p>
                  <p className="text-sm">
                    <strong>Supported browsers:</strong>
                  </p>
                  <ul className="text-sm mt-1 ml-4 list-disc">
                    <li>Google Chrome (recommended)</li>
                    <li>Microsoft Edge</li>
                    <li>Safari (macOS/iOS)</li>
                  </ul>
                  <p className="text-sm mt-2 text-muted-foreground">
                    Note: Firefox currently does not support the Web Speech API.
                  </p>
                </AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center gap-2">
        <Button
          variant={isListening ? "default" : "outline"}
          size="sm"
          onClick={toggleListening}
          disabled={isLoading}
          className="relative"
        >
          {isListening ? (
            <>
              <Mic className="w-4 h-4 mr-2" />
              <span className="animate-pulse">Listening...</span>
              <motion.div
                className="absolute -inset-0.5 bg-primary/20 rounded-md"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
            </>
          ) : (
            <>
              <MicOff className="w-4 h-4 mr-2" />
              Voice Control
            </>
          )}
        </Button>
        
        <Popover
          trigger={
            <Button variant="ghost" size="icon">
              <Settings2 className="w-4 h-4" />
            </Button>
          }
          content={
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Voice Settings</h4>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="continuous">Continuous listening</Label>
                  <Switch
                    id="continuous"
                    checked={continuous}
                    onCheckedChange={setContinuous}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="sound">Sound effects</Label>
                  <Switch
                    id="sound"
                    checked={soundEnabled}
                    onCheckedChange={setSoundEnabled}
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="language">Language</Label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger id="language" className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="en-GB">English (UK)</SelectItem>
                    <SelectItem value="es-ES">Spanish</SelectItem>
                    <SelectItem value="fr-FR">French</SelectItem>
                    <SelectItem value="de-DE">German</SelectItem>
                    <SelectItem value="it-IT">Italian</SelectItem>
                    <SelectItem value="pt-BR">Portuguese (Brazil)</SelectItem>
                    <SelectItem value="zh-CN">Chinese (Simplified)</SelectItem>
                    <SelectItem value="ja-JP">Japanese</SelectItem>
                    <SelectItem value="ko-KR">Korean</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCommands(!showCommands)}
                  className="w-full"
                >
                  {showCommands ? 'Hide' : 'Show'} Available Commands
                </Button>
              </div>
            </div>
          }
        />
      </div>
      
      {/* Transcript Display */}
      <AnimatePresence>
        {(transcript || interimTranscript) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-2 left-0 right-0"
          >
            <Card>
              <CardContent className="p-3">
                <div className="flex items-start gap-2">
                  <Volume2 className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm">
                      {transcript && <span className="font-medium">{transcript}</span>}
                      {interimTranscript && <span className="text-muted-foreground"> {interimTranscript}</span>}
                    </p>
                    {lastCommand && (
                      <div className="flex items-center gap-1 mt-1">
                        <CheckCircle2 className="w-3 h-3 text-green-500" />
                        <span className="text-xs text-green-600">Command: {lastCommand}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Error Display */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-2 left-0 right-0"
          >
            <Card className="border-destructive/50">
              <CardContent className="p-3">
                <div className="flex items-center gap-2">
                  <AlertCircle className="w-4 h-4 text-destructive" />
                  <p className="text-sm text-destructive">{error}</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Commands List */}
      <AnimatePresence>
        {showCommands && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full mt-12 left-0 right-0 z-50"
          >
            <Card>
              <CardContent className="p-4">
                <h4 className="text-sm font-medium mb-2">Available Voice Commands</h4>
                <div className="space-y-1">
                  {VOICE_COMMANDS.map((cmd, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-xs">
                      <Zap className="w-3 h-3 text-primary" />
                      <span className="text-muted-foreground">{cmd.description}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}