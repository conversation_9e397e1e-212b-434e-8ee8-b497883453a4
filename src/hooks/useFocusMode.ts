import { useState, useEffect, useCallback } from 'react';
import { useFeatureFlags } from '@/lib/featureFlags';

export interface FocusModeConfig {
  enabled: boolean;
  essentialFeatures: string[];
  contextualFeatures: string[];
  hiddenFeatures: string[];
  customPresets: FocusModePreset[];
  activePreset: string | null;
}

export interface FocusModePreset {
  id: string;
  name: string;
  description: string;
  features: string[];
  icon?: string;
}

const DEFAULT_PRESETS: FocusModePreset[] = [
  {
    id: 'minimal',
    name: 'Minimal',
    description: 'Only the absolute essentials',
    features: ['projects', 'agents'],
    icon: 'minimize'
  },
  {
    id: 'development',
    name: 'Development',
    description: 'Focus on coding tasks',
    features: ['projects', 'agents', 'templates', 'performance'],
    icon: 'code'
  },
  {
    id: 'analysis',
    name: 'Analysis',
    description: 'Performance and usage analysis',
    features: ['projects', 'usage-dashboard', 'performance', 'mcp'],
    icon: 'chart'
  },
  {
    id: 'collaboration',
    name: 'Collaboration',
    description: 'Team-focused features',
    features: ['projects', 'agents', 'templates', 'plugins'],
    icon: 'users'
  }
];

const STORAGE_KEY = 'claudia-focus-mode';

export function useFocusMode() {
  const { flags } = useFeatureFlags();
  const [config, setConfig] = useState<FocusModeConfig>(() => {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (e) {
        console.error('Failed to parse focus mode config:', e);
      }
    }
    
    return {
      enabled: false,
      essentialFeatures: ['projects', 'agents'],
      contextualFeatures: [],
      hiddenFeatures: [],
      customPresets: [],
      activePreset: null
    };
  });

  // Save config to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(config));
    
    // Dispatch event for other components to react
    window.dispatchEvent(new CustomEvent('focus-mode-changed', { 
      detail: { enabled: config.enabled, config } 
    }));
  }, [config]);

  const toggleFocusMode = useCallback(() => {
    setConfig(prev => ({ ...prev, enabled: !prev.enabled }));
  }, []);

  const setFocusMode = useCallback((enabled: boolean) => {
    setConfig(prev => ({ ...prev, enabled }));
  }, []);

  const applyPreset = useCallback((presetId: string) => {
    const preset = [...DEFAULT_PRESETS, ...config.customPresets].find(p => p.id === presetId);
    if (preset) {
      setConfig(prev => ({
        ...prev,
        essentialFeatures: preset.features,
        activePreset: presetId,
        enabled: true
      }));
    }
  }, [config.customPresets]);

  const saveCustomPreset = useCallback((preset: FocusModePreset) => {
    setConfig(prev => ({
      ...prev,
      customPresets: [...prev.customPresets.filter(p => p.id !== preset.id), preset]
    }));
  }, []);

  const deleteCustomPreset = useCallback((presetId: string) => {
    setConfig(prev => ({
      ...prev,
      customPresets: prev.customPresets.filter(p => p.id !== presetId),
      activePreset: prev.activePreset === presetId ? null : prev.activePreset
    }));
  }, []);

  const updateEssentialFeatures = useCallback((features: string[]) => {
    setConfig(prev => ({
      ...prev,
      essentialFeatures: features,
      activePreset: null // Clear preset when manually updating
    }));
  }, []);

  const isFeatureVisible = useCallback((feature: string) => {
    if (!config.enabled) return true;
    
    // Check if feature is enabled in feature flags
    const featureKey = feature.replace('-', '') as keyof typeof flags;
    if (flags[featureKey] && !flags[featureKey].enabled) {
      return false;
    }
    
    return config.essentialFeatures.includes(feature) || 
           config.contextualFeatures.includes(feature);
  }, [config, flags]);

  const getVisibleFeatures = useCallback(() => {
    if (!config.enabled) {
      // Return all enabled features when focus mode is off
      return Object.keys(flags).filter(key => flags[key as keyof typeof flags]?.enabled);
    }
    
    return [...config.essentialFeatures, ...config.contextualFeatures]
      .filter(feature => {
        const featureKey = feature.replace('-', '') as keyof typeof flags;
        return !flags[featureKey] || flags[featureKey].enabled;
      });
  }, [config, flags]);

  const getAllPresets = useCallback(() => {
    return [...DEFAULT_PRESETS, ...config.customPresets];
  }, [config.customPresets]);

  return {
    config,
    enabled: config.enabled,
    toggleFocusMode,
    setFocusMode,
    applyPreset,
    saveCustomPreset,
    deleteCustomPreset,
    updateEssentialFeatures,
    isFeatureVisible,
    getVisibleFeatures,
    getAllPresets,
    activePreset: config.activePreset,
    presets: getAllPresets()
  };
}