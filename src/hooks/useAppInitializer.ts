import { useState, useEffect } from 'react';
import { useUIStore } from '@/stores/uiStore';
import { api } from '@/lib/api';

type InitializationStatus = 'initializing' | 'ready' | 'error';

export function useAppInitializer() {
  const [status, setStatus] = useState<InitializationStatus>('initializing');
  const { setLoading, setError } = useUIStore();

  useEffect(() => {
    const initialize = async () => {
      try {
        setLoading(true);
        
        // Perform all startup operations here
        await api.checkClaudeVersion();
        // You can add other essential startup calls here, for example:
        // await api.getThemeConfig();
        
        setStatus('ready');
      } catch (err) {
        console.error("Application initialization failed:", err);
        setError("Failed to initialize the application. Please try restarting.");
        setStatus('error');
      } finally {
        setLoading(false);
      }
    };

    initialize();
  }, [setLoading, setError]);

  return status;
}