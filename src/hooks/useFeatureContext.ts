import { useState, useEffect, useCallback } from 'react';

export interface FeatureContext {
  currentView: string;
  recentViews: string[];
  activeProject: string | null;
  projectType: ProjectType | null;
  sessionDuration: number;
  lastActivity: Date;
  suggestedFeatures: string[];
}

export type ProjectType = 
  | 'web-frontend'
  | 'web-backend'
  | 'api'
  | 'mobile'
  | 'desktop'
  | 'data-science'
  | 'devops'
  | 'general';

const CONTEXT_STORAGE_KEY = 'claudia-feature-context';
const RECENT_VIEWS_LIMIT = 5;

export function useFeatureContext(currentView: string, activeProject?: string | null) {
  const [context, setContext] = useState<FeatureContext>(() => {
    const stored = localStorage.getItem(CONTEXT_STORAGE_KEY);
    if (stored) {
      try {
        const parsed = JSON.parse(stored);
        return {
          ...parsed,
          lastActivity: new Date(parsed.lastActivity)
        };
      } catch (e) {
        console.error('Failed to parse feature context:', e);
      }
    }
    
    return {
      currentView,
      recentViews: [currentView],
      activeProject: null,
      projectType: null,
      sessionDuration: 0,
      lastActivity: new Date(),
      suggestedFeatures: []
    };
  });

  // Update current view and recent views
  useEffect(() => {
    setContext(prev => {
      const recentViews = [currentView, ...prev.recentViews.filter(v => v !== currentView)]
        .slice(0, RECENT_VIEWS_LIMIT);
      
      return {
        ...prev,
        currentView,
        recentViews,
        lastActivity: new Date()
      };
    });
  }, [currentView]);

  // Update active project
  useEffect(() => {
    if (activeProject !== undefined) {
      setContext(prev => ({
        ...prev,
        activeProject,
        projectType: activeProject ? detectProjectType(activeProject) : null
      }));
    }
  }, [activeProject]);

  // Track session duration
  useEffect(() => {
    const interval = setInterval(() => {
      setContext(prev => ({
        ...prev,
        sessionDuration: prev.sessionDuration + 1
      }));
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  // Save context to localStorage
  useEffect(() => {
    localStorage.setItem(CONTEXT_STORAGE_KEY, JSON.stringify(context));
  }, [context]);

  // Detect project type based on project files/structure
  const detectProjectType = useCallback((projectPath: string): ProjectType => {
    // This is a simplified detection - in a real implementation,
    // you'd analyze the project files and structure
    const lowercasePath = projectPath.toLowerCase();
    
    if (lowercasePath.includes('react') || lowercasePath.includes('vue') || lowercasePath.includes('angular')) {
      return 'web-frontend';
    }
    if (lowercasePath.includes('api') || lowercasePath.includes('backend')) {
      return 'web-backend';
    }
    if (lowercasePath.includes('mobile') || lowercasePath.includes('ios') || lowercasePath.includes('android')) {
      return 'mobile';
    }
    if (lowercasePath.includes('ml') || lowercasePath.includes('data')) {
      return 'data-science';
    }
    
    return 'general';
  }, []);

  // Get contextual feature suggestions
  const getSuggestedFeatures = useCallback((): string[] => {
    const suggestions: string[] = [];
    
    // Always suggest current view
    suggestions.push(context.currentView);
    
    // Based on project type
    switch (context.projectType) {
      case 'web-frontend':
        suggestions.push('templates', 'plugins', 'performance');
        break;
      case 'web-backend':
        suggestions.push('agents', 'mcp', 'performance');
        break;
      case 'api':
        suggestions.push('agents', 'mcp', 'usage-dashboard');
        break;
      case 'mobile':
        suggestions.push('agents', 'templates', 'performance');
        break;
      case 'data-science':
        suggestions.push('agents', 'performance', 'usage-dashboard');
        break;
      default:
        suggestions.push('agents', 'templates');
    }
    
    // Based on session duration
    if (context.sessionDuration > 30) {
      suggestions.push('performance', 'usage-dashboard');
    }
    
    // Based on recent views (suggest complementary features)
    if (context.recentViews.includes('agents')) {
      suggestions.push('projects', 'templates');
    }
    if (context.recentViews.includes('projects')) {
      suggestions.push('agents', 'performance');
    }
    
    // Remove duplicates and limit to top 5
    return [...new Set(suggestions)].slice(0, 5);
  }, [context]);

  // Update suggested features periodically
  useEffect(() => {
    const suggestions = getSuggestedFeatures();
    setContext(prev => ({
      ...prev,
      suggestedFeatures: suggestions
    }));
  }, [context.currentView, context.projectType, context.sessionDuration, getSuggestedFeatures]);

  const resetContext = useCallback(() => {
    setContext({
      currentView,
      recentViews: [currentView],
      activeProject: null,
      projectType: null,
      sessionDuration: 0,
      lastActivity: new Date(),
      suggestedFeatures: []
    });
  }, [currentView]);

  return {
    context,
    suggestedFeatures: context.suggestedFeatures,
    projectType: context.projectType,
    sessionDuration: context.sessionDuration,
    resetContext
  };
}