// SuperClaude Settings Type Definitions and Utilities

export interface SuperClaudeSettings {
  // Overall enable/disable toggle
  enabled: boolean;
  
  // Core Modes
  coreModes: {
    introspection: boolean;
    ultraCompressed: boolean;
    tokenEconomy: boolean;
    costOptimization: boolean;
  };
  
  // Thinking Depth
  thinkingDepth: {
    level: 'minimal' | 'standard' | 'deep' | 'exhaustive';
    chainOfThought: boolean;
    multiStepReasoning: boolean;
    selfCorrection: boolean;
  };
  
  // MCP Servers
  mcpServers: {
    filesystem: boolean;
    github: boolean;
    memory: boolean;
    brave_search: boolean;
    puppeteer: boolean;
    sequential_thinking: boolean;
    everart: boolean;
    serena: boolean;
    context7: boolean;
    mult_fetch: boolean;
    redis: boolean;
    swift_sdk: boolean;
  };
  
  // Personas
  personas: {
    active: 'default' | 'architect' | 'debugger' | 'reviewer' | 'researcher' | 'optimizer' | 'documenter';
    autoSwitch: boolean;
    collaborationMode: boolean;
  };
  
  // Planning
  planning: {
    enabled: boolean;
    autoPlanning: boolean;
    taskBreakdown: boolean;
    milestoneTracking: boolean;
  };
  
  // Security & Quality
  security: {
    owaspCompliance: boolean;
    codeReview: boolean;
    vulnerabilityScanning: boolean;
    sanitization: 'strict' | 'standard' | 'relaxed';
  };
  
  // Commands & Workflows
  commands: {
    batchOperations: boolean;
    parallelExecution: boolean;
    autoRetry: boolean;
    errorRecovery: boolean;
  };
  
  // Output Preferences
  output: {
    format: 'verbose' | 'standard' | 'compressed' | 'minimal';
    codeComments: 'detailed' | 'standard' | 'minimal';
    explanations: boolean;
    tokenUsageTracking: boolean;
  };
  
  // Performance
  performance: {
    caching: boolean;
    lazyLoading: boolean;
    batchSize: number;
    parallelism: number;
  };
  
  // Session
  session: {
    autoSave: boolean;
    contextRetention: 'full' | 'smart' | 'minimal';
    historySize: number;
  };
  
  // Quick Presets
  preset?: 'efficient' | 'thorough' | 'creative' | 'secure' | 'custom';
}

// Default SuperClaude Configuration
export const defaultSuperClaudeSettings: SuperClaudeSettings = {
  enabled: true,
  
  coreModes: {
    introspection: false,
    ultraCompressed: false,
    tokenEconomy: true,
    costOptimization: true,
  },
  
  thinkingDepth: {
    level: 'standard',
    chainOfThought: true,
    multiStepReasoning: false,
    selfCorrection: true,
  },
  
  mcpServers: {
    filesystem: true,
    github: true,
    memory: false,
    brave_search: false,
    puppeteer: false,
    sequential_thinking: false,
    everart: false,
    serena: false,
    context7: false,
    mult_fetch: false,
    redis: false,
    swift_sdk: false,
  },
  
  personas: {
    active: 'default',
    autoSwitch: true,
    collaborationMode: false,
  },
  
  planning: {
    enabled: true,
    autoPlanning: true,
    taskBreakdown: true,
    milestoneTracking: false,
  },
  
  security: {
    owaspCompliance: true,
    codeReview: true,
    vulnerabilityScanning: false,
    sanitization: 'standard',
  },
  
  commands: {
    batchOperations: true,
    parallelExecution: true,
    autoRetry: true,
    errorRecovery: true,
  },
  
  output: {
    format: 'standard',
    codeComments: 'standard',
    explanations: true,
    tokenUsageTracking: false,
  },
  
  performance: {
    caching: true,
    lazyLoading: true,
    batchSize: 10,
    parallelism: 4,
  },
  
  session: {
    autoSave: true,
    contextRetention: 'smart',
    historySize: 100,
  },
};

// Quick Presets
export const superClaudePresets: Record<string, Partial<SuperClaudeSettings>> = {
  efficient: {
    coreModes: {
      introspection: false,
      ultraCompressed: true,
      tokenEconomy: true,
      costOptimization: true,
    },
    thinkingDepth: {
      level: 'minimal',
      chainOfThought: false,
      multiStepReasoning: false,
      selfCorrection: false,
    },
    output: {
      format: 'compressed',
      codeComments: 'minimal',
      explanations: false,
      tokenUsageTracking: true,
    },
  },
  
  thorough: {
    coreModes: {
      introspection: true,
      ultraCompressed: false,
      tokenEconomy: false,
      costOptimization: false,
    },
    thinkingDepth: {
      level: 'exhaustive',
      chainOfThought: true,
      multiStepReasoning: true,
      selfCorrection: true,
    },
    planning: {
      enabled: true,
      autoPlanning: true,
      taskBreakdown: true,
      milestoneTracking: true,
    },
  },
  
  creative: {
    personas: {
      active: 'architect',
      autoSwitch: true,
      collaborationMode: true,
    },
    thinkingDepth: {
      level: 'deep',
      chainOfThought: true,
      multiStepReasoning: true,
      selfCorrection: true,
    },
    mcpServers: {
      filesystem: true,
      github: true,
      memory: true,
      brave_search: true,
      puppeteer: false,
      sequential_thinking: true,
      everart: true,
      serena: false,
      context7: true,
      mult_fetch: true,
      redis: false,
      swift_sdk: false,
    },
  },
  
  secure: {
    security: {
      owaspCompliance: true,
      codeReview: true,
      vulnerabilityScanning: true,
      sanitization: 'strict',
    },
    personas: {
      active: 'reviewer',
      autoSwitch: false,
      collaborationMode: false,
    },
    output: {
      format: 'verbose',
      codeComments: 'detailed',
      explanations: true,
      tokenUsageTracking: true,
    },
  },
};

// Helper Functions
export function applyPreset(preset: keyof typeof superClaudePresets): SuperClaudeSettings {
  return {
    ...defaultSuperClaudeSettings,
    ...superClaudePresets[preset],
    preset: preset as any,
  };
}

export function settingsToCommandArgs(settings: SuperClaudeSettings): string[] {
  const args: string[] = [];
  
  // Core modes (custom SuperClaude options - not passed to Claude CLI)
  
  // Thinking depth (custom SuperClaude options - not passed to Claude CLI)
  
  // MCP servers (custom SuperClaude option - not passed to Claude CLI)
  // Claude Code uses MCP configuration files instead of command-line arguments
  
  // Personas (custom SuperClaude options - not passed to Claude CLI)
  
  // All remaining options are custom SuperClaude settings that don't exist in Claude CLI
  // Claude Code accepts: --output-format (text|json|stream-json), and various standard options
  
  // Only keep valid Claude CLI options - map custom format to valid values
  const formatMap = {
    'verbose': 'text',
    'standard': 'text', 
    'compressed': 'json',
    'minimal': 'json'
  };
  args.push(`--output-format=${formatMap[settings.output.format as keyof typeof formatMap] || 'text'}`);
  
  return args;
}

// Local Storage Management
const STORAGE_KEY = 'superClaude_settings';

export function saveSettings(settings: SuperClaudeSettings): void {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
}

export function loadSettings(): SuperClaudeSettings {
  const stored = localStorage.getItem(STORAGE_KEY);
  if (stored) {
    try {
      return { ...defaultSuperClaudeSettings, ...JSON.parse(stored) };
    } catch {
      return defaultSuperClaudeSettings;
    }
  }
  return defaultSuperClaudeSettings;
}

// Token Usage Estimation
export function estimateTokenUsage(settings: SuperClaudeSettings): {
  inputMultiplier: number;
  outputMultiplier: number;
  estimatedCostMultiplier: number;
} {
  let inputMultiplier = 1.0;
  let outputMultiplier = 1.0;
  
  // Core modes affect token usage
  if (settings.coreModes.ultraCompressed) {
    inputMultiplier *= 0.6;
    outputMultiplier *= 0.5;
  }
  if (settings.coreModes.introspection) {
    inputMultiplier *= 1.3;
    outputMultiplier *= 1.4;
  }
  
  // Thinking depth
  const depthMultipliers = {
    minimal: 0.7,
    standard: 1.0,
    deep: 1.5,
    exhaustive: 2.0,
  };
  const depthMult = depthMultipliers[settings.thinkingDepth.level];
  inputMultiplier *= depthMult;
  outputMultiplier *= depthMult;
  
  // Output format
  const formatMultipliers = {
    minimal: 0.6,
    compressed: 0.8,
    standard: 1.0,
    verbose: 1.4,
  };
  outputMultiplier *= formatMultipliers[settings.output.format];
  
  // MCP servers add overhead
  const activeMcpCount = Object.values(settings.mcpServers).filter(Boolean).length;
  inputMultiplier *= (1 + activeMcpCount * 0.05);
  
  // Calculate cost multiplier (approximate)
  const estimatedCostMultiplier = (inputMultiplier * 0.3 + outputMultiplier * 0.7);
  
  return {
    inputMultiplier,
    outputMultiplier,
    estimatedCostMultiplier,
  };
}

// Export settings as JSON
export function exportSettings(settings: SuperClaudeSettings): string {
  return JSON.stringify(settings, null, 2);
}

// Import settings from JSON
export function importSettings(json: string): SuperClaudeSettings | null {
  try {
    const imported = JSON.parse(json);
    return { ...defaultSuperClaudeSettings, ...imported };
  } catch {
    return null;
  }
}